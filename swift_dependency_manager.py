#!/usr/bin/env python3
"""
Swift Package Dependency Manager

This script:
1. Scans an input folder for Swift packages and maps their provided libraries
2. Scans a target folder for Swift files and finds import statements
3. Checks Package.swift files and adds missing local dependencies
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Set, Tuple, Optional
import argparse


class SwiftPackageManager:
    def __init__(self):
        self.library_map: Dict[str, str] = {}  # library_name -> package_path
        self.library_to_pkgname: Dict[str, str] = {}  # library_name -> package_name

    def find_swift_packages(self, input_folder: str) -> Dict[str, str]:
        """
        Phase 1: Find all Swift packages and create a map of libraries to their paths
        """
        print(f"🔍 Scanning for Swift packages in: {input_folder}")

        for root, dirs, files in os.walk(input_folder):
            # Skip .build directories and their subdirectories
            dirs[:] = [d for d in dirs if not self._should_ignore_directory(d)]

            if "Package.swift" in files:
                package_path = Path(root)
                package_file = package_path / "Package.swift"
                libraries = self._extract_libraries_from_package(package_file)
                package_name = self._extract_package_name(package_file)

                for library in libraries:
                    self.library_map[library] = str(package_path)
                    if package_name:
                        self.library_to_pkgname[library] = package_name
                    print(f"  📦 Found library '{library}' in {package_path}")

        print(f"✅ Found {len(self.library_map)} libraries in {len(set(self.library_map.values()))} packages")
        return self.library_map

    def _extract_libraries_from_package(self, package_file: Path) -> List[str]:
        """Extract library names from Package.swift file"""
        libraries = []
        try:
            with open(package_file, 'r', encoding='utf-8') as f:
                content = f.read()
            # Look for .library patterns
            library_pattern = r'\.library\(\s*name:\s*"([^"]+)"'
            libraries.extend(re.findall(library_pattern, content))
            # Also look for .target patterns as they might expose libraries
            target_pattern = r'\.target\(\s*name:\s*"([^"]+)"'
            targets = re.findall(target_pattern, content)
            # Filter out test targets and common non-library targets
            for target in targets:
                if not target.lower().endswith('tests') and not target.lower().endswith('test'):
                    libraries.append(target)
        except Exception as e:
            print(f"⚠️  Error reading {package_file}: {e}")
        return list(set(libraries))  # Remove duplicates

    def _extract_package_name(self, package_file: Path) -> str:
        """Extract the Package.name from a Package.swift file."""
        try:
            with open(package_file, 'r', encoding='utf-8') as f:
                content = f.read()
            m = re.search(r'Package\s*\(\s*name:\s*"([^"]+)"', content)
            if m:
                return m.group(1)
        except Exception as e:
            print(f"⚠️  Error reading package name from {package_file}: {e}")
        return ""

    def find_imports_in_target(self, target_folder: str) -> Set[str]:
        """
        Phase 2: Find all import statements in Swift files within the main target folder
        """
        print(f"🔍 Scanning for imports in Swift files: {target_folder}")

        all_imports = self._collect_imports_in_dir(target_folder)

        # Filter imports to only those we have local packages for
        relevant_imports = all_imports.intersection(self.library_map.keys())

        print(f"✅ Found {len(all_imports)} total imports, {len(relevant_imports)} match local packages")
        for imp in sorted(relevant_imports):
            print(f"  📥 Import: {imp} -> {self.library_map[imp]}")

        return relevant_imports

    def _extract_imports_from_swift_file(self, swift_file: Path) -> Set[str]:
        """Extract import statements from a Swift file"""
        imports = set()
        
        try:
            with open(swift_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Find all import statements with better regex
            import_pattern = r'(?:^|\n)\s*(?:@[_A-Za-z]+\s+)*import\s+(?:(?:class|struct|enum|protocol|typealias|func|var|let)\s+)?([A-Za-z_][A-Za-z0-9_]*)'
            
            for match in re.finditer(import_pattern, content, re.MULTILINE):
                module_name = match.group(1)
                # Skip system frameworks
                if not self._is_system_framework(module_name):
                    imports.add(module_name)
                    
        except Exception as e:
            print(f"⚠️  Error reading {swift_file}: {e}")
        
        return imports

    def _is_system_framework(self, module_name: str) -> bool:
        """Check if a module is a system framework"""
        system_frameworks = {
            'Foundation', 'UIKit', 'SwiftUI', 'Combine', 'CoreData', 'CoreGraphics',
            'QuartzCore', 'AVFoundation', 'CoreLocation', 'MapKit', 'UserNotifications',
            'StoreKit', 'GameplayKit', 'SpriteKit', 'SceneKit', 'ARKit', 'RealityKit',
            'CloudKit', 'HealthKit', 'HomeKit', 'EventKit', 'Contacts', 'Photos',
            'AssetsLibrary', 'MediaPlayer', 'AVKit', 'WebKit', 'SafariServices',
            'MessageUI', 'Social', 'Accounts', 'Twitter', 'FacebookSDK', 'CoreMotion',
            'CoreBluetooth', 'ExternalAccessory', 'GameController', 'MultipeerConnectivity',
            'NetworkExtension', 'NotificationCenter', 'CallKit', 'Intents', 'IntentsUI',
            'CoreML', 'Vision', 'NaturalLanguage', 'Speech', 'SoundAnalysis',
            'BackgroundTasks', 'WidgetKit', 'AppClip', 'OSLog', 'os', 'Darwin', 'Swift'
        }
        return module_name in system_frameworks
    def _should_ignore_directory(self, dir_name: str) -> bool:
        """Check if a directory should be ignored during scanning"""
        ignored_directories = {
            '.build',           # Swift build artifacts
            '.git',            # Git repository data
            '.svn',            # SVN repository data
            'node_modules',    # Node.js dependencies
            'build',           # General build directories
            'Build',           # Xcode build directories
            'DerivedData',     # Xcode derived data
            '.DS_Store',       # macOS metadata (though this is a file, not dir)
            'Pods',            # CocoaPods dependencies
            'Carthage',        # Carthage dependencies
            '__pycache__',     # Python cache
            '.pytest_cache',   # Pytest cache
            'venv',            # Python virtual environments
            '.venv',           # Python virtual environments
            'env',             # Environment directories
            '.env',            # Environment directories
        }
        return dir_name in ignored_directories

    def update_package_swift(self, target_folder: str, relevant_imports: Set[str], dry_run: bool = False) -> bool:
        """
        Phase 3: Update Package.swift with missing local dependencies
        """
        package_file = Path(target_folder) / "Package.swift"

        if not package_file.exists():
            print(f"❌ No Package.swift found in {target_folder}")
            return False

        print(f"📝 Checking Package.swift: {package_file}")

        try:
            with open(package_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # Find existing and missing dependencies
            existing_deps = self._find_existing_local_dependencies(content, target_folder)
            missing_deps = relevant_imports - existing_deps

            print(f"📋 Existing local dependencies: {existing_deps}")
            if missing_deps:
                print(f"➕ Missing package-level dependencies: {missing_deps}")

            # Analyze target-level dependencies
            missing_by_target = self._find_missing_target_dependencies(content, target_folder)

            # Check if any changes are needed
            if not missing_deps and not missing_by_target:
                print("✅ All required dependencies are already present!")
                return True

            if dry_run:
                self._print_dry_run_summary(missing_deps, missing_by_target)
                return True

            # Apply changes
            updated_content = self._apply_dependency_updates(content, target_folder, missing_deps, missing_by_target)

            # Create backup and write updated content
            self._write_updated_package(package_file, content, updated_content)

            print("✅ Package.swift updated successfully!")
            return True

        except Exception as e:
            print(f"❌ Error updating Package.swift: {e}")
            return False

    def _find_existing_local_dependencies(self, content: str, target_folder: str) -> Set[str]:
        """Find existing local path dependencies in Package.swift and map them to known local modules.

        We parse .package(path: "…") entries, resolve them to absolute paths relative to the target_folder,
        and then match those paths against the manager's library_map to derive which modules those packages expose.
        """
        existing: Set[str] = set()
        try:
            # Capture all .package(path: "…") occurrences and extract the path value
            path_pattern = r'\.package\(\s*path:\s*"([^"]+)"\s*\)'
            declared_paths = re.findall(path_pattern, content)
            if not declared_paths:
                return existing

            # Build reverse index: real package path -> set of module names provided there
            path_to_libs: Dict[str, Set[str]] = {}
            for lib, pkg_path in self.library_map.items():
                real_pkg = os.path.realpath(pkg_path)
                if real_pkg not in path_to_libs:
                    path_to_libs[real_pkg] = set()
                path_to_libs[real_pkg].add(lib)

            for dep_path in declared_paths:
                # Resolve relative paths against target_folder; support absolute paths too
                if os.path.isabs(dep_path):
                    abs_dep_path = os.path.realpath(dep_path)
                else:
                    abs_dep_path = os.path.realpath(os.path.join(target_folder, dep_path))

                libs_here = path_to_libs.get(abs_dep_path)
                if libs_here:
                    existing.update(libs_here)
        except Exception as e:
            print(f"⚠️  Error parsing existing local dependencies: {e}")

        return existing

    def _find_missing_target_dependencies(self, content: str, target_folder: str) -> Dict[str, Set[str]]:
        """Find missing target-level dependencies by analyzing each target's imports"""
        targets = self._parse_targets(content)
        target_names = {t['name'] for t in targets}
        missing_by_target: Dict[str, Set[str]] = {}

        print(f"🔎 Analyzing target dependencies:")

        for target in targets:
            target_name = target['name']
            source_dir = self._target_source_dir(target, target_folder)

            # Get imports from this target's source files
            target_imports = self._collect_imports_in_dir(source_dir)
            local_imports = {imp for imp in target_imports if imp in self.library_map}

            # Filter imports based on target type rules
            allowed_imports = {imp for imp in local_imports if self._is_module_allowed_for_target(target, imp)}

            # Get existing dependencies for this target
            existing_deps = self._find_existing_target_products(content, target_name)

            # Get transitive dependencies from internal target dependencies
            transitive_deps = self._get_transitive_dependencies(content, target_name, target_names)

            # Find missing dependencies
            missing = allowed_imports - existing_deps - transitive_deps

            print(f"  🎯 Target '{target_name}': {len(allowed_imports)} imports, {len(existing_deps)} existing, {len(missing)} missing")

            if missing:
                missing_by_target[target_name] = missing

        return missing_by_target

    def _is_module_allowed_for_target(self, target: Dict[str, str], module: str) -> bool:
        """Determine if a module is allowed for a specific target type"""
        target_name = target.get('name', '')
        target_kind = target.get('kind', 'target')

        # TestsUtils modules should only be used by test targets and other TestsUtils targets
        if module.endswith('TestsUtils'):
            return target_kind == 'testTarget' or target_name.endswith('TestsUtils')

        # Regular modules can be used by any target
        return True

    def _get_transitive_dependencies(self, content: str, target_name: str, target_names: Set[str]) -> Set[str]:
        """Get modules provided by internal target dependencies"""
        internal_deps = self._parse_internal_target_deps(content, target_names)
        existing_products_map = {t: self._find_existing_target_products(content, t) for t in target_names}

        def _collect_transitive(tn: str, seen: Set[str] = None) -> Set[str]:
            if seen is None:
                seen = set()
            if tn in seen:
                return set()
            seen.add(tn)

            result = set()
            for dep_target in internal_deps.get(tn, set()):
                if dep_target in target_names:
                    result.add(dep_target)
                    result.update(existing_products_map.get(dep_target, set()))
                    result.update(_collect_transitive(dep_target, seen))
            return result

        return _collect_transitive(target_name)

    def _print_dry_run_summary(self, missing_deps: Set[str], missing_by_target: Dict[str, Set[str]]):
        """Print summary of what would be changed in dry run mode"""
        if missing_deps:
            print(f"\n➕ Package-level dependencies that would be added: {sorted(missing_deps)}")

        if missing_by_target:
            print("\n➕ Target-level dependencies that would be added:")
            for tname, mods in sorted(missing_by_target.items()):
                for mod in sorted(mods):
                    pkg_name = self.library_to_pkgname.get(mod, mod)
                    if self._should_use_product_syntax(mod, set()):
                        print(f'  {tname}: .product(name: "{mod}", package: "{pkg_name}")')
                    else:
                        print(f'  {tname}: "{mod}"')

        print("\n🔍 DRY RUN: No changes made to Package.swift")

    def _apply_dependency_updates(self, content: str, target_folder: str, missing_deps: Set[str], missing_by_target: Dict[str, Set[str]]) -> str:
        """Apply all dependency updates to the Package.swift content"""
        updated_content = content

        # Add package-level dependencies
        if missing_deps:
            print(f"\n➕ Adding package-level dependencies: {sorted(missing_deps)}")
            updated_content = self._add_dependencies_to_package(updated_content, missing_deps, target_folder)

        # Add target-level dependencies
        if missing_by_target:
            print(f"\n➕ Adding target-level dependencies:")
            for tname, mods in sorted(missing_by_target.items()):
                print(f"  {tname}: {sorted(mods)}")

            target_names = {t['name'] for t in self._parse_targets(updated_content)}
            updated_content = self._add_products_to_targets(updated_content, missing_by_target, target_folder, target_names)

        # Final cleanup: remove duplicates and normalize
        updated_content = self._dedupe_target_dependency_arrays(updated_content)
        return updated_content

    def _write_updated_package(self, package_file: Path, original_content: str, updated_content: str):
        """Create backup and write updated Package.swift"""
        # Create backup
        backup_file = package_file.with_suffix('.swift.backup')
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write(original_content)
        print(f"💾 Created backup: {backup_file}")

        # Write updated content
        with open(package_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)

    def _add_dependencies_to_package(self, content: str, missing_deps: Set[str], target_folder: str) -> str:
        """Add missing .package(path: ...) dependencies to the top-level dependencies array.
        - Dedupe by package path
        - Skip if already declared anywhere in the manifest
        - Preserve indentation
        """
        if not missing_deps:
            return content

        # Collect all existing declared relative paths to avoid adding duplicates anywhere in the file
        existing_rel_paths = set(re.findall(r'\.package\(\s*path:\s*"([^"]+)"\s*\)', content))

        # Build a unique list of package paths to add
        unique_by_path: Dict[str, str] = {}
        for dep in sorted(missing_deps):
            dep_path = self.library_map.get(dep)
            if not dep_path:
                continue
            relative_path = os.path.relpath(dep_path, target_folder)
            if relative_path in existing_rel_paths:
                continue
            if relative_path not in unique_by_path:
                unique_by_path[relative_path] = dep

        # Nothing to add
        if not unique_by_path:
            return content

        lines = content.split('\n')

        # Find the top-level dependencies: [ ... ] section
        deps_line_idx = -1
        for i, line in enumerate(lines):
            if 'dependencies:' in line and '[' in line:
                deps_line_idx = i
                break

        if deps_line_idx == -1:
            # Could not find; just print guidance and return original content
            print("⚠️  Could not find dependencies section in Package.swift")
            print("📝 You may need to manually add these dependencies:")
            for relative_path, dep in sorted(unique_by_path.items()):
                print(f"  .package(path: \"{relative_path}\"),  // for import {dep}")
            return content

        # Determine indentation based on existing entries within dependencies array
        base_indent = re.match(r'^(\s*)', lines[deps_line_idx]).group(1)
        entry_indent = None
        closing_idx = None
        for j in range(deps_line_idx + 1, len(lines)):
            if re.search(r'\]\s*[,)]?\s*$', lines[j]):
                closing_idx = j
                break
            if re.search(r'\.package\s*\(', lines[j]):
                entry_indent = re.match(r'^(\s*)', lines[j]).group(1)
                break
        insert_indent = entry_indent if entry_indent is not None else (base_indent + '    ')

        # Prepare new lines to insert, with an optional comment header (only once)
        new_lines = []
        comment_text = f"{insert_indent}// OGKit local packages actually imported:"
        # Avoid duplicate comment if already present nearby
        neighborhood = "\n".join(lines[max(0, deps_line_idx-3):min(len(lines), (closing_idx or deps_line_idx)+3)])
        if "OGKit local packages actually imported" not in neighborhood:
            new_lines.append(comment_text)
        new_lines.extend([f"{insert_indent}.package(path: \"{rel}\")," for rel in sorted(unique_by_path.keys())])

        # Insert right after the dependencies: [ line
        insertion_point = deps_line_idx + 1
        lines[insertion_point:insertion_point] = new_lines

        return '\n'.join(lines)
    def _parse_targets(self, content: str) -> List[Dict[str, str]]:
        """Parse target and testTarget entries from Package.swift content.
        Returns list of dicts with keys: name, kind (target|testTarget), path (may be empty).
        """
        targets: List[Dict[str, str]] = []
        pattern = re.compile(r'\.(target|testTarget)\s*\(\s*name:\s*"([^"]+)"', re.MULTILINE)
        for m in pattern.finditer(content):
            kind, name = m.group(1), m.group(2)
            window = content[m.start(): m.start() + 600]
            path_m = re.search(r'path:\s*"([^"]+)"', window)
            path_val = path_m.group(1) if path_m else ""
            targets.append({"kind": kind, "name": name, "path": path_val})
        return targets

    def _parse_internal_target_deps(self, content: str, target_names: Set[str]) -> Dict[str, Set[str]]:
        """Parse per-target internal target dependencies (references to other targets in this package).
        Returns mapping target_name -> set(of internal target names it depends on).
        """
        deps_map: Dict[str, Set[str]] = {}
        lines = content.splitlines()
        # Precompile for speed
        target_decl = re.compile(r'\.(?:target|testTarget)\s*\(.*name:\s*"([^"]+)"')
        for i, line in enumerate(lines):
            m = target_decl.search(line)
            if not m:
                continue
            name = m.group(1)
            # Find end of this target block and dependencies body
            depth = 0
            start_idx = i
            end_idx = i
            deps_body = None
            for j in range(start_idx, len(lines)):
                depth += lines[j].count('(')
                depth -= lines[j].count(')')
                if deps_body is None and 'dependencies:' in lines[j]:
                    # Collect until matching closing bracket of deps array
                    body_lines = []
                    k = j
                    bracket_depth = 0
                    # find '[' on same line or subsequent
                    while k < len(lines) and '[' not in lines[k]:
                        k += 1
                    if k < len(lines):
                        for z in range(k, len(lines)):
                            body_lines.append(lines[z])
                            bracket_depth += lines[z].count('[')
                            bracket_depth -= lines[z].count(']')
                            if bracket_depth <= 0:
                                break
                        deps_body = '\n'.join(body_lines)
                end_idx = j
                if j > start_idx and depth <= 0:
                    break
            refs: Set[str] = set()
            if deps_body:
                # String style: "Name"
                for sm in re.finditer(r'"([A-Za-z_][A-Za-z0-9_]*)"', deps_body):
                    val = sm.group(1)
                    if val in target_names:
                        refs.add(val)
                # Explicit .target(name: "Name") style
                for tm in re.finditer(r'\.target\(\s*name:\s*"([^"]+)"', deps_body):
                    val = tm.group(1)
                    if val in target_names:
                        refs.add(val)
            deps_map[name] = refs
        return deps_map



    def _target_source_dir(self, target: Dict[str, str], target_folder: str) -> str:
        """Get the source directory for a target with better path resolution"""
        target_name = target.get('name', '')
        target_kind = target.get('kind', 'target')

        # Check if target has explicit path first
        if target.get('path'):
            explicit_path = target['path']
            full_path = os.path.join(target_folder, explicit_path)
            if os.path.exists(full_path):
                return full_path

        # Handle test targets - they're typically in Tests/TargetName
        if target_kind == 'testTarget':
            tests_target_path = os.path.join(target_folder, 'Tests', target_name)
            if os.path.exists(tests_target_path):
                return tests_target_path

        # Standard Swift package structure - try Sources/TargetName first
        sources_target_path = os.path.join(target_folder, 'Sources', target_name)
        if os.path.exists(sources_target_path):
            return sources_target_path

        # If no specific target directory exists, fall back to Sources
        sources_path = os.path.join(target_folder, 'Sources')
        if os.path.exists(sources_path):
            return sources_path

        # Final fallback to target folder
        return target_folder

    def _collect_imports_in_dir(self, dir_path: str) -> Set[str]:
        """Collect all imports from Swift files in a directory"""
        imports = set()

        if not os.path.exists(dir_path):
            return imports

        swift_files_found = 0
        for root, dirs, files in os.walk(dir_path):
            # Skip .build and other ignored directories
            dirs[:] = [d for d in dirs if not self._should_ignore_directory(d)]

            for file in files:
                if file.endswith('.swift'):
                    swift_files_found += 1
                    swift_file = Path(root) / file
                    file_imports = self._extract_imports_from_swift_file(swift_file)
                    imports.update(file_imports)

        return imports

    def _should_use_product_syntax(self, module: str, target_names: Set[str] = None) -> bool:
        # Internal targets (targets within the same package) should always use string syntax
        if target_names and module in target_names:
            return False

        # Prefer .product syntax for test utility modules and similar cases from external packages
        if module.endswith("TestsUtils"):
            return True
        return False

    def _find_existing_target_products(self, content: str, target_name: str) -> Set[str]:
        """Find existing target-level dependencies (both string and .product) for a target.
        Robust to multi-line declarations where name: appears on a following line.
        """
        lines = content.splitlines()
        existing: Set[str] = set()

        i = 0
        while i < len(lines):
            # Detect start of any target/testTarget block
            if not re.search(r'\.(?:target|testTarget)\s*\(', lines[i]):
                i += 1
                continue

            # Look ahead to find the name for this block
            tname = None
            for la in range(i, min(i + 80, len(lines))):
                nm = re.search(r'name:\s*"([^"]+)"', lines[la])
                if nm:
                    tname = nm.group(1)
                    break
            if tname != target_name:
                # Skip this block
                # advance to end of this block by paren depth
                depth = 0
                j = i
                while j < len(lines):
                    depth += lines[j].count('(')
                    depth -= lines[j].count(')')
                    if j > i and depth <= 0:
                        break
                    j += 1
                i = j + 1
                continue

            # We found the block for target_name; capture its full text
            start_idx = i
            depth = 0
            end_idx = start_idx
            for j in range(start_idx, len(lines)):
                depth += lines[j].count('(')
                depth -= lines[j].count(')')
                end_idx = j
                if j > start_idx and depth <= 0:
                    break
            block = '\n'.join(lines[start_idx:end_idx+1])

            # Parse .product(name:)
            for m in re.finditer(r'\.product\(\s*name:\s*"([^"]+)"', block):
                existing.add(m.group(1))

            # Parse string-style deps within dependencies array (robust across lines)
            deps_body = None
            dep_idx = block.find('dependencies')
            if dep_idx != -1:
                # Find the '[' that starts the array
                lb = block.find('[', dep_idx)
                if lb != -1:
                    bracket_depth = 0
                    body = []
                    for ch in block[lb:]:
                        body.append(ch)
                        if ch == '[':
                            bracket_depth += 1
                        elif ch == ']':
                            bracket_depth -= 1
                            if bracket_depth == 0:
                                break
                    deps_body = ''.join(body)
            if deps_body:
                for sm in re.finditer(r'"([A-Za-z_][A-Za-z0-9_]*)"', deps_body):
                    name = sm.group(1)
                    if name in self.library_map:
                        existing.add(name)

            return existing

        return existing

    def _add_products_to_targets(self, content: str, missing_by_target: Dict[str, Set[str]], target_folder: str, target_names: Set[str] = None) -> str:
        """Insert missing target-level dependencies into each target's dependencies array.
        - Uses string style by default ("Module")
        - Uses .product(name:, package:) for modules needing product syntax (e.g., *TestsUtils)
        - Robust to multi-line .target/testTarget declarations
        """
        lines = content.split('\n')
        i = 0
        while i < len(lines):
            # Detect start of a target/testTarget block even if name: is on following lines
            if not re.search(r'\.(?:target|testTarget)\s*\(', lines[i]):
                i += 1
                continue


            # Find the target name by scanning ahead a reasonable window
            tname = None
            for look_ahead in range(i, min(i + 50, len(lines))):
                nm = re.search(r'name:\s*"([^"]+)"', lines[look_ahead])
                if nm:
                    tname = nm.group(1)
                    break
            if not tname:
                i += 1
                continue

            missing = sorted(missing_by_target.get(tname, []))
            # Advance past the block either way to keep scanning stable
            start_idx = i
            depth = 0
            end_idx = start_idx
            deps_line_idx = -1
            for j in range(start_idx, len(lines)):
                depth += lines[j].count('(')
                depth -= lines[j].count(')')
                if deps_line_idx == -1 and 'dependencies:' in lines[j] and '[' in lines[j]:
                    deps_line_idx = j
                end_idx = j
                if j > start_idx and depth <= 0:
                    break

            if not missing:
                i = end_idx + 1
                continue

            if deps_line_idx == -1:
                print(f"\u26a0\ufe0f  Target '{tname}' has no dependencies: [] section; skipping insertion for this target.")
                i = end_idx + 1
                continue

            indent = re.match(r'^(\s*)', lines[deps_line_idx]).group(1) + '    '
            new_lines = []
            for module in missing:
                if self._should_use_product_syntax(module, target_names):
                    package_name = self.library_to_pkgname.get(module, module)
                    new_lines.append(f'{indent}.product(name: "{module}", package: "{package_name}"),')
                else:
                    new_lines.append(f'{indent}"{module}",')

            # Insert right after the 'dependencies: [' line
            insertion_point = deps_line_idx + 1
            lines[insertion_point:insertion_point] = new_lines

            # Move index past the end of the block plus inserted lines
            i = end_idx + len(new_lines) + 1

        return '\n'.join(lines)


    def _dedupe_target_dependency_arrays(self, content: str) -> str:
        """Remove duplicate entries within each target's dependencies array and normalize representation.
        - For modules ending with TestsUtils: keep .product(name:, package:) only (one entry)
        - For other known modules: keep string style "Module" only (one entry)
        - For unknown names: keep first occurrence (string or product) and drop exact duplicates
        """
        lines = content.split('\n')
        i = 0
        while i < len(lines):
            if not re.search(r'\.(?:target|testTarget)\s*\(', lines[i]):
                i += 1
                continue
            # Find block bounds and dependencies array
            start_idx = i
            depth = 0
            end_idx = start_idx
            deps_line_idx = -1
            lb_idx = -1
            rb_idx = -1
            for j in range(start_idx, len(lines)):
                depth += lines[j].count('(')
                depth -= lines[j].count(')')
                if deps_line_idx == -1 and 'dependencies' in lines[j]:
                    deps_line_idx = j
                if deps_line_idx != -1 and lb_idx == -1 and '[' in lines[j]:
                    lb_idx = j
                    bracket_depth = 0
                    for k in range(j, len(lines)):
                        bracket_depth += lines[k].count('[')
                        bracket_depth -= lines[k].count(']')
                        if bracket_depth <= 0:
                            rb_idx = k
                            break
                    break
                end_idx = j
                if j > start_idx and depth <= 0:
                    break
            if lb_idx == -1 or rb_idx == -1:
                i = end_idx + 1
                continue
            original_body = lines[lb_idx:rb_idx+1]
            indent_match = re.match(r'^(\s*)', original_body[0])
            base_indent = indent_match.group(1) if indent_match else ''
            entry_indent = base_indent + '    '
            def desired_kind(name: str) -> str:
                if name.endswith('TestsUtils'):
                    return 'p'
                if name in self.library_map:
                    return 's'
                return 'any'
            first_kind: Dict[str, str] = {}
            first_package: Dict[str, str] = {}
            for line in original_body[1:-1]:
                m_str = re.match(r'\s*"([A-Za-z_][A-Za-z0-9_]*)"\s*,?\s*$', line)
                m_prod = re.match(r'\s*\.product\(\s*name:\s*"([^"]+)"\s*,\s*package:\s*"([^"]+)"[^)]*\)\s*,?\s*$', line)
                if m_prod:
                    name = m_prod.group(1)
                    pkg = m_prod.group(2)
                    if name not in first_kind:
                        dk = desired_kind(name)
                        first_kind[name] = 'p' if dk in ('p','any') else 's'
                        first_package[name] = pkg
                elif m_str:
                    name = m_str.group(1)
                    if name not in first_kind:
                        dk = desired_kind(name)
                        first_kind[name] = 's' if dk in ('s','any') else 'p'
            rebuilt: List[str] = []
            emitted: Set[str] = set()
            for line in original_body[1:-1]:
                m_str = re.match(r'\s*"([A-Za-z_][A-Za-z0-9_]*)"\s*,?\s*$', line)
                m_prod = re.match(r'\s*\.product\(\s*name:\s*"([^"]+)"\s*,\s*package:\s*"([^"]+)"[^)]*\)\s*,?\s*$', line)
                name = None
                kind = None
                pkg = None
                if m_prod:
                    name = m_prod.group(1)
                    pkg = m_prod.group(2)
                    kind = 'p'
                elif m_str:
                    name = m_str.group(1)
                    kind = 's'
                if name is None:
                    rebuilt.append(line)
                    continue
                dk = desired_kind(name)
                target_kind = 'p' if dk == 'p' else ('s' if dk == 's' else first_kind.get(name, kind))
                if name in emitted:
                    continue
                emitted.add(name)
                if target_kind == 'p':
                    package_name = self.library_to_pkgname.get(name, first_package.get(name, pkg or name))
                    rebuilt.append(f'{entry_indent}.product(name: "{name}", package: "{package_name}"),')
                else:
                    rebuilt.append(f'{entry_indent}"{name}",')
            new_body = [original_body[0]] + rebuilt + [original_body[-1]]
            lines[lb_idx:rb_idx+1] = new_body
            i = end_idx - (len(original_body) - len(new_body)) + 1
        return '\n'.join(lines)



    def run(self, input_folder: str, target_folder: str, dry_run: bool = False):
        """Run the complete dependency management process"""
        print("🚀 Starting Swift Package Dependency Manager")
        if dry_run:
            print("🔍 DRY RUN MODE - No files will be modified")
        print()

        # Phase 1: Discover all Swift packages and their libraries
        self.find_swift_packages(input_folder)
        print()

        # Phase 2: Find imports in the main target folder
        relevant_imports = self.find_imports_in_target(target_folder)
        print()

        # Phase 3: Update Package.swift with missing dependencies
        if relevant_imports or not dry_run:  # Always check in non-dry-run mode
            success = self.update_package_swift(target_folder, relevant_imports, dry_run)
            if not success:
                print("❌ Failed to update Package.swift")
                return
        else:
            print("ℹ️  No matching imports found - nothing to add to Package.swift")

        print(f"\n🎉 Process completed {'(dry run)' if dry_run else ''}!")

def main():
    parser = argparse.ArgumentParser(description='Swift Package Dependency Manager')
    parser.add_argument('input_folder', help='Folder to scan for Swift packages')
    parser.add_argument('target_folder', help='Target folder with Swift files and Package.swift')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without making changes')

    args = parser.parse_args()

    if not os.path.exists(args.input_folder):
        print(f"❌ Input folder does not exist: {args.input_folder}")
        return 1

    if not os.path.exists(args.target_folder):
        print(f"❌ Target folder does not exist: {args.target_folder}")
        return 1

    manager = SwiftPackageManager()
    manager.run(args.input_folder, args.target_folder, args.dry_run)
    return 0


if __name__ == '__main__':
    exit(main())
