// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
  name: "AppTracker",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "AppTracker",
      targets: ["AppTracker"]
    ),
    .library(
      name: "AppTrackerTestsUtils",
      targets: ["AppTrackerTestsUtils"]
    )
  ],
  dependencies: [
    // OGKit local packages actually imported:
    .package(path: "../../OGKit/Packages/OGAirshipKit"),
    .package(path: "../../OGKit/Packages/OGCore"),
    .package(path: "../../OGKit/Packages/OGCore/Packages/OGMacros"),
    .package(path: "../../OGKit/Packages/OGCore/Packages/OGMock"),
    .package(path: "../../OGKit/Packages/OGDialogCoordinator/Packages/OGScreenViewUpdate"),
    .package(path: "../../OGKit/Packages/OGDomainStore"),
    .package(path: "../../OGKit/Packages/OGNavigation"),
    .package(path: "../../OGKit/Packages/OGNavigation/Packages/OGNavigationCore"),
    .package(path: "../../OGKit/Packages/OGNavigationBar"),
    .package(path: "../../OGKit/Packages/OGRouter"),
    .package(path: "../../OGKit/Packages/OGSystemKit"),
    .package(path: "../../OGKit/Packages/OGTracker"),
    .package(path: "../../OGKit/Packages/OGTracker/Packages/OGTrackerCore"),
    .package(path: "../../OGKit/Packages/OGTracker/Packages/OGTrackerOptInService"),
    .package(path: "../../OGKit/Packages/OGWebView"),
    .package(path: "../AppCore"),
    .package(path: "../ExternalDependencies")
  ],
  targets: [
    .target(
      name: "AppTracker",
      dependencies: [
          "OGMacros",
          "OGMock",
          "OGNavigation",
          "AppCore",
          "ExternalDependencies",
      ],
      swiftSettings: [
        .enableExperimentalFeature("StrictConcurrency=minimal")
      ]
    ),
    .target(
      name: "AppTrackerTestsUtils",
      dependencies: [
          "OGMacros",
          "OGMock",
          "OGNavigation",
          "AppTracker",
          .product(name: "AppCoreTestsUtils", package: "AppCore"),
      ],
      path: "TestsUtils",
      swiftSettings: [
        .enableExperimentalFeature("StrictConcurrency=minimal")
      ]
    ),
    .testTarget(
      name: "AppTrackerTests",
      dependencies: [
          "OGCore",
          .product(name: "OGCoreTestsUtils", package: "OGCore"),
          "OGNavigationCore",
          .product(name: "OGNavigationCoreTestsUtils", package: "OGNavigationCore"),
          "OGTrackerOptInService",
          .product(name: "OGTrackerOptInServiceTestsUtils", package: "OGTrackerOptInService"),
          .product(name: "AppTrackerTestsUtils", package: "AppTrackerTestsUtils"),
      ],
      swiftSettings: [
        .enableExperimentalFeature("StrictConcurrency=minimal")
      ]
    )
  ]
)

