// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
  name: "AppTracker",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "AppTracker",
      targets: ["AppTracker"]
    ),
    .library(
      name: "AppTrackerTestsUtils",
      targets: ["AppTrackerTestsUtils"]
    )
  ],
  dependencies: [
    .package(path: "../AppCore"),
    .package(path: "../ExternalDependencies")
  ],
  targets: [
    .target(
      name: "AppTracker",
      dependencies: [
        "AppCore",
        "ExternalDependencies"
      ],
      swiftSettings: [
        .enableExperimentalFeature("StrictConcurrency=minimal")
      ]
    ),
    .target(
      name: "AppTrackerTestsUtils",
      dependencies: [
        "AppTracker",
        .product(name: "AppCoreTestsUtils", package: "AppCore")
      ],
      path: "TestsUtils",
      swiftSettings: [
        .enableExperimentalFeature("StrictConcurrency=minimal")
      ]
    ),
    .testTarget(
      name: "AppTrackerTests",
      dependencies: [
        "AppTrackerTestsUtils"
      ],
      swiftSettings: [
        .enableExperimentalFeature("StrictConcurrency=minimal")
      ]
    )
  ]
)

